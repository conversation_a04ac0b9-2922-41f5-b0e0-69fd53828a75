import { FC, useCallback, useEffect, useMemo, useState } from 'react';
import { FormProvider, useForm } from 'react-hook-form';

import { yupResolver } from '@hookform/resolvers/yup';
import clsx from 'clsx';
import * as yup from 'yup';

import { Accordion, ButtonSize, HTMLContent, SideDrawer } from '@oh/components';

import {
  Button,
  ButtonVariant,
  Icon,
  Loader,
  Typography,
  TypographyVariant
} from '@/components';
import { OneTalentButtonVariant } from '@/components/button/Button';
import { ErrorScreen } from '@/components/ErrorScreen';
import { RichTextEditor } from '@/components/textareas';
import {
  TEXT_PLACEHOLDER,
  TEXT_SHORT_LENGTH,
  TOO_LONG_TEXT
} from '@/constants';
import { useBreakpointsContext } from '@/contexts';

import { RegenerateDrawer } from './RegenerateDrawer';

import { useGenerateAIMeetingOutcome } from '../../domain';
import { GenerateAIMeetingOutcomeRequestModel } from '../../domain/check-ins/models';

export interface MeetingOutcomeAIDrawerProps {
  meetingOutcome: string;
  topics: string[] | undefined;
  title: string | undefined;
  discussionDetails: string | undefined;
  meetingTranscript: string | undefined;
  applyChanges: (meetingOutcome: string) => void;
  closeDrawer: () => void;
}

interface MeetingOutcomeAIForm {
  providedMeetingOutcome?: string;
  prompt?: string;

  generatedMeetingOutcome?: string;
}

const schema = yup.object({
  providedMeetingOutcome: yup
    .string()
    .max(TEXT_SHORT_LENGTH, TOO_LONG_TEXT)
    .optional()
});

export const MeetingOutcomeAIDrawer: FC<MeetingOutcomeAIDrawerProps> = ({
  meetingOutcome,
  meetingTranscript,
  discussionDetails,
  title,
  topics,
  applyChanges,
  closeDrawer
}) => {
  const { isMobile } = useBreakpointsContext();
  const [isRegenerateDrawerOpen, setIsRegenerateDrawerOpen] = useState(false);
  const [generatedMeetingOutcome, setGeneratedMeetingOutcome] = useState<
    string | undefined
  >(undefined);

  const defaultFormData = useMemo<MeetingOutcomeAIForm>(
    () => ({
      providedMeetingOutcome: meetingOutcome
    }),
    [meetingOutcome]
  );

  const formContext = useForm<MeetingOutcomeAIForm>({
    mode: 'onChange',
    resolver: yupResolver(schema),
    defaultValues: defaultFormData
  });

  const { watch } = formContext;

  const [generateRequest, setGenerateRequest] =
    useState<GenerateAIMeetingOutcomeRequestModel>({
      meetingOutcome,
      topics,
      title,
      discussionDetails,
      meetingTranscript
    });

  const { isPending, isError, mutate } = useGenerateAIMeetingOutcome();

  const generate = useCallback(() => {
    mutate(generateRequest, {
      onSuccess: (summary) => {
        setGeneratedMeetingOutcome(summary);
      }
    });
  }, [generateRequest, mutate]);

  useEffect(() => {
    generate();
  }, [generate]);

  const onApplyChanges = useCallback(() => {
    if (generatedMeetingOutcome) {
      applyChanges(generatedMeetingOutcome);
    }

    closeDrawer();
  }, [applyChanges, closeDrawer, generatedMeetingOutcome]);

  const openRegenerateDrawer = useCallback(() => {
    setIsRegenerateDrawerOpen(true);
  }, []);

  const closeRegenerateDrawer = useCallback(() => {
    setIsRegenerateDrawerOpen(false);
  }, []);

  const providedMeetingOutcome = watch('providedMeetingOutcome');
  const regenerate = useCallback(
    (prompt: string) => {
      setGenerateRequest((prev) => ({
        ...prev,
        prompt,
        meetingOutcome: providedMeetingOutcome
      }));

      closeRegenerateDrawer();
    },
    [closeRegenerateDrawer, providedMeetingOutcome]
  );

  const onBack = useCallback(() => {
    closeDrawer();
  }, [closeDrawer]);

  const content = useMemo(() => {
    if (isPending) {
      return <LoadingContent />;
    }

    if (isError) {
      return <ErrorContent tryAgain={generate} />;
    }

    return <FormContent generatedMeetingOutcome={generatedMeetingOutcome} />;
  }, [isPending, isError, generatedMeetingOutcome, generate]);

  return (
    <SideDrawer
      isOpen={true}
      title="Generate with AI"
      onBack={onBack}
      className="!md:mt-0 !relative !mt-40"
      headerClassName="bg-surface-grey_0"
      titleClassName="!text-[20px] !leading-[24px] justify-center md:justify-start"
      shouldCloseOnOverlayClick={false}
      shouldCloseOnEsc={false}
      isSwipeDisabled={true}
    >
      <FormProvider {...formContext}>
        <form className="flex h-full flex-col justify-between overflow-hidden bg-surface-grey_10">
          {content}

          <div className="flex flex-row items-stretch justify-end gap-[16px] rounded-[20px] bg-surface-grey_0 px-20 pb-48 pt-16 shadow-oh_light md:rounded-[12px] md:p-20">
            <Button
              variant={OneTalentButtonVariant.PrimaryPurple}
              size={isMobile ? ButtonSize.Medium : ButtonSize.Large}
              disabled={isPending || isError}
              className={clsx(!isMobile && 'w-full', 'min-w-[130px]')}
              onClick={openRegenerateDrawer}
            >
              <Typography
                className="flex flex-row items-center gap-4"
                variant={TypographyVariant.Cta1Medium}
              >
                <Icon name="aiFill" /> Regenerate
              </Typography>
            </Button>
            <Button
              size={isMobile ? ButtonSize.Medium : ButtonSize.Large}
              disabled={isPending || isError}
              className={clsx(!isMobile && 'w-full', 'min-w-[130px]')}
              variant={ButtonVariant.Primary}
              onClick={onApplyChanges}
            >
              <Typography
                className="flex flex-row items-center gap-4"
                variant={TypographyVariant.Cta1Medium}
              >
                Apply Changes
              </Typography>
            </Button>
          </div>
        </form>
      </FormProvider>
      <RegenerateDrawer
        isOpen={isRegenerateDrawerOpen}
        regenerate={regenerate}
        closeDrawer={closeRegenerateDrawer}
      />
    </SideDrawer>
  );
};

interface FormContentProps {
  generatedMeetingOutcome: string | undefined;
}

const FormContent: FC<FormContentProps> = ({ generatedMeetingOutcome }) => {
  const { isMobile } = useBreakpointsContext();

  return (
    <div
      data-attributes="MeetingOutcomeAIDrawer"
      className="flex flex-col gap-12 overflow-y-auto p-20"
    >
      <Accordion
        isExpanded
        title="Meeting Outcome (Provided by you)*"
        bodyClassName="mt-16"
        className="rounded-xl !bg-surface-grey_0 p-16"
      >
        <RichTextEditor
          labelTypographyVariant={TypographyVariant.Body2Regular}
          label={isMobile ? 'Meeting Outcome (Provided by you)*' : undefined}
          hideLabelFromTrigger
          required
          name="providedMeetingOutcome"
          editorClassName="text-[16px] leading-[22px] max-h-[238px] overflow-y-auto min-h-[88px]"
          placeholder={TEXT_PLACEHOLDER}
        />
      </Accordion>

      {generatedMeetingOutcome !== undefined && (
        <div className="gap-10 flex flex-col rounded-xl bg-surface-purple p-16">
          <Typography
            className="flex flex-row items-center gap-4 p-4 text-tag-purple_heart-text"
            variant={TypographyVariant.Cta2Regular}
          >
            <Icon name="aiFillSmall" /> Enhanced with AI
          </Typography>

          <Typography
            className="text-tag-purple_heart-text"
            variant={TypographyVariant.Body1Regular}
          >
            <HTMLContent className="preview-elements">
              {generatedMeetingOutcome}
            </HTMLContent>
          </Typography>
        </div>
      )}
    </div>
  );
};

const LoadingContent: FC = () => {
  return (
    <div
      data-attributes="MeetingOutcomeAIDrawer"
      className="m-20 flex h-full flex-col gap-16 rounded-xl bg-surface-grey_0 p-16"
    >
      <div className="flex-grow">
        <Loader description="AI is generating Meeting Outcome" />
      </div>
    </div>
  );
};

export interface ErrorContentProps {
  tryAgain: () => void;
}

const ErrorContent: FC<ErrorContentProps> = ({ tryAgain }) => {
  return (
    <div
      data-attributes="MeetingOutcomeAIDrawer"
      className="m-20 flex h-full flex-col gap-16 rounded-xl bg-surface-grey_0 p-16"
    >
      <div className="flex-grow">
        <ErrorScreen
          titleClassName="!m-0 !text-[18px] !leading-[28px]"
          subtitleClassName="!m-0"
          title="Sorry, an error occurred"
          subtitle="AI is not available"
        />
      </div>
      <div className="flex flex-row justify-center">
        <Button variant={ButtonVariant.Link} onClick={tryAgain}>
          Try Again
        </Button>
      </div>
    </div>
  );
};
