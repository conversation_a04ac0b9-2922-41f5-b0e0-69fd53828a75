import { FC, ReactElement } from 'react';
import { BrowserRouter, Route, Routes } from 'react-router-dom';

import { createRoot } from 'react-dom/client';

import { unregister } from '@/service-worker';

import '@oh/components/dist/styles.css';

import { AppModeContext } from './contexts';
import { FeedbackApp } from './FeedbackApp';
import { FeedbackApprovalApp } from './FeedbackApprovalApp';
import { FederationAppProps } from './withApplication';
import { withApprovalQuery } from './withApprovalQuery';

const props = {
  options: {},
  messageToHostFunction: () => null
} as unknown as FederationAppProps;

const Bootstrap: FC = (): ReactElement => {
  return (
    <AppModeContext.Provider value={{ isStandaloneMode: true }}>
      <BrowserRouter basename={'/one-talent'}>
        <Routes>
          <Route path="/*" element={<FeedbackApp {...props} />} />
          <Route path="/Approvals" element={<FeedbackApprovalAppComponent />} />
        </Routes>
      </BrowserRouter>
      <BrowserRouter basename={'/feedback-management'}>
        <Routes>
          <Route path="/*" element={<FeedbackApp {...props} />} />
          <Route path="/Approvals" element={<FeedbackApprovalAppComponent />} />
        </Routes>
      </BrowserRouter>
    </AppModeContext.Provider>
  );
};

const FeedbackApprovalAppComponent = withApprovalQuery(FeedbackApprovalApp);

const container: HTMLDivElement | null = document.querySelector('div');

if (!container) {
  throw new Error("The element wasn't found");
}

createRoot(container).render(<Bootstrap />);

unregister();
