import { FC } from 'react';

import { useQuery } from './hooks';
import { FederationAppProps } from './withApplication';

const props = {
  options: {},
  messageToHostFunction: () => null
} as unknown as FederationAppProps;

export const withApprovalQuery =
  (WrappedComponent: FC<FederationAppProps>) => () => {
    const query = useQuery();

    const entityId = query.get('entityId');
    const entityType = query.get('entityType');
    const requestId = query.get('requestId');
    const isTracking = Boolean(query.get('isTracking'));

    return (
      <WrappedComponent
        {...props}
        options={
          {
            entityId,
            entityType,
            isTracking,
            inboxItemData: { externalId: requestId }
          } as unknown as FederationAppProps['options']
        }
      />
    );
  };
