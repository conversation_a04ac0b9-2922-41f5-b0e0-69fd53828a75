import { lazy, useMemo } from 'react';

import { ErrorScreen } from '@oh/components';

import type { INonIndexRouteObject } from '@/contracts/router';

const FeedbackApproval = lazy(async () => ({
  default: (await import('@/feedback/pages/FeedbackApprovalPage'))
    .FeedbackApprovalPage
}));

export type UseFeedbackApprovalRoutesProps = {
  requestId?: string;
  entityType?: string;
};

export const useFeedbackApprovalRoutes = ({
  requestId,
  entityType
}: UseFeedbackApprovalRoutesProps) => {
  return useMemo(() => {
    if (!requestId || !entityType) {
      return [errorRoute];
    }

    if (entityType === 'Feedback') {
      return getFeedbackApprovalRoutes(requestId);
    }

    return [errorRoute];
  }, [entityType, requestId]);
};

const getFeedbackApprovalRoutes = (
  requestId: string
): INonIndexRouteObject[] => [
  {
    path: '/',
    element: <FeedbackApproval requestId={requestId} />
  },
  errorRoute
];

const errorRoute: INonIndexRouteObject = {
  path: '*',
  element: <ErrorScreen className="!h-auto" />
};
