import { FC, useCallback, useEffect, useMemo, useState } from 'react';
import { FormProvider } from 'react-hook-form';

import { Informer } from '@ot/onetalent-ui-kit';
import { UseQueryResult } from '@tanstack/react-query';

import { ErrorScreen } from '@oh/components';
import { ActionHistoryItem } from '@oh/contracts/action-history';

import {
  ActionHistory,
  ActionIds,
  ActionModel,
  Actions,
  ApprovalActionHandler,
  ApprovalHeader,
  FullScreenLoaderWrapped,
  Text
} from '@/components';
import { Requestor } from '@/components/approvalHeader/components';
import { useAppContext } from '@/contexts';
import { FeedbackType } from '@/feedback/constants';
import {
  FeedbackStatus,
  DeclineFeedbackFormModel,
  useCurrentEmployeeContext,
  useFeedbackAlignedChain,
  useFeedbackApprovalActions,
  useFeedbackApprovalHistory,
  useFeedbackApprovalItem,
  useFeedbackApprovalSubmit
} from '@/feedback/domain';
import { transformToApprovalHistory } from '@/feedback/domain/feedbackApproval/transformers/toApprovalHistory';

import { DeclineFeedbackModal, RequestDetailsSection } from './components';
import { YourFeedbackSection } from './components/YourFeedbackSection';
import { useFeedbackApprovalForm } from './hooks/useFeedbackApprovalForm';

const ACTION_OVERRIDES = {
  [ActionIds.Decline]: { isCustomHandled: true },
  [ActionIds.Finalize]: { isCustomHandled: true }
};

export const FeedbackApproval: FC<{ requestId: string }> = ({ requestId }) => {
  const [isDeclineModalOpen, setDeclineModalOpen] = useState(false);
  const {
    data: approvalItem,
    isSuccess,
    isError,
    isFetching: isItemFetching
  } = useFeedbackApprovalItem({
    requestId
  });
  const { options: { isTracking } = {} } = useAppContext();
  const {
    data: chain,
    isLoading: isChainLoading,
    isError: isChainError
  } = useFeedbackAlignedChain({ requestId });
  const historyResult = useFeedbackApprovalHistory({ requestId });
  const actionsResult = useFeedbackApprovalActions({ requestId });
  const { employee } = useCurrentEmployeeContext();

  const alignedHistoryResult = useMemo(
    () =>
      ({
        ...historyResult,
        data: transformToApprovalHistory(historyResult.data)
      }) as UseQueryResult<ActionHistoryItem[]>,
    [historyResult]
  );
  const isGiverView = approvalItem?.giver?.employeeId === employee.employeeId;
  const isReceiverView =
    approvalItem?.receiver?.employeeId === employee.employeeId;
  const isRequestedByManager =
    approvalItem?.receiver?.employeeId !== approvalItem?.requestor?.employeeId;
  const isPendingStatus = approvalItem?.status.id === FeedbackStatus.Pending;
  const isReadonly =
    approvalItem?.status.id !== FeedbackStatus.Pending || !!isTracking;
  const showFeedbackSection =
    (!isReceiverView && !isReadonly) ||
    approvalItem?.rating ||
    approvalItem?.feedbackText;

  const methods = useFeedbackApprovalForm({
    readonly: isReadonly,
    approvalItem
  });
  const {
    formState: { isValid },
    handleSubmit
  } = methods;

  useEffect(() => {
    methods.reset(approvalItem);
  }, [approvalItem, methods]);

  const { mutate, isPending: isSubmitting } = useFeedbackApprovalSubmit({
    requestId
  });

  const isPageLoading =
    isSubmitting ||
    isItemFetching ||
    isChainLoading ||
    historyResult.isFetching ||
    actionsResult.isFetching;

  const normalizedActionsResult = useMemo(
    () =>
      ({
        ...actionsResult,
        data: actionsResult.data
          ? actionsResult.data.map((item) => ({
              ...item,
              ...(item.actionId &&
              item.actionId === ActionIds.Finalize &&
              !isValid
                ? {
                    disabled: true,
                    message: 'Please fill in all the mandatory fields'
                  }
                : undefined)
            }))
          : []
      }) as UseQueryResult<ActionModel[]>,
    [actionsResult, isValid]
  );

  const handleCloseDeclineModal = useCallback(
    () => setDeclineModalOpen(false),
    []
  );

  const handleActionSubmit = useCallback(
    ({ declineReason, comment }: DeclineFeedbackFormModel) => {
      handleCloseDeclineModal();
      mutate({ declineReason, comment, actionId: ActionIds.Decline });
    },
    [mutate, handleCloseDeclineModal]
  );

  const handleAction = useCallback<ApprovalActionHandler>(
    (actionId, comment) => {
      switch (actionId) {
        case ActionIds.Decline:
          setDeclineModalOpen(true);
          break;
        case ActionIds.Finalize:
          handleSubmit((values) => mutate({ actionId, ...values }))();
          break;
        default:
          mutate({
            actionId,
            comment
          });
      }
    },
    [handleSubmit, mutate]
  );

  const requestedFor = useMemo<Requestor | undefined>(() => {
    const { receiver, requestor } = approvalItem ?? {};
    if (receiver?.employeeId === requestor?.employeeId) {
      return undefined;
    }
    return {
      email: receiver?.email ?? '',
      jobTitle: receiver?.jobTitle ?? '',
      name: receiver?.fullNameEnglish ?? ''
    };
  }, [approvalItem]);

  if (isPageLoading) {
    return <FullScreenLoaderWrapped />;
  }

  if (isError) {
    return <ErrorScreen />;
  }

  return (
    <>
      <FormProvider {...methods}>
        <div className="flex min-h-full flex-col @container">
          <div className="flex grow flex-col gap-20 px-20 pb-24 md:gap-24 md:px-24 md:pt-20">
            <Text className="hidden text-body-xl-medium md:block">
              Feedback Request
            </Text>
            <ApprovalHeader
              requestedFor={requestedFor}
              chainContainerClassName="-mx-20 mb-20"
              requestedByClassName="bg-surface-grey_0 !rounded-2xl"
              chain={chain}
              isFetching={isChainLoading}
              isError={isChainError}
              className="mx-20"
            >
              {isGiverView && isRequestedByManager && isPendingStatus && (
                <Informer
                  className="mb-20 md:mb-24"
                  variant="Info"
                  title="Feedback Visibility Management"
                  description="After you submit your feedback, the requestor will have the option to either share it with the receiver or keep it confidential. Please take this into account when drafting your feedback to ensure it aligns with your intended purpose"
                />
              )}
            </ApprovalHeader>
            {isSuccess && <RequestDetailsSection {...approvalItem} />}
            {showFeedbackSection && (
              <YourFeedbackSection
                type={
                  isReceiverView ? FeedbackType.Received : FeedbackType.Given
                }
                item={approvalItem}
              />
            )}
            <ActionHistory
              containerClassName="!my-0 bg-surface-grey_0 !rounded-2xl"
              historyRequest={alignedHistoryResult}
              isApprovals
              userLoadMode="off"
            />
          </div>
          <Actions
            actionsOverrides={ACTION_OVERRIDES}
            actionsResult={normalizedActionsResult}
            actionHandler={handleAction}
            className="pb-48 pt-16 md:mx-24"
          />
        </div>
      </FormProvider>
      <DeclineFeedbackModal
        isOpen={isDeclineModalOpen}
        onAction={handleActionSubmit}
        onCancel={handleCloseDeclineModal}
      />
    </>
  );
};
