import { useCallback } from 'react';

import { useQuery } from '@tanstack/react-query';

import { useATRServiceApi } from '@/atr/eoy-assessment/domain/api';
import { logger } from '@/logger';

import { WORKFLOWS_REQUEST_KEYS } from './WorkflowsRequestKeys';

import { AssessmentRole } from '../models';
import { transformAssessmentRole } from '../transformers';

function useGetAssessmentRole() {
  const { endOfYearAssessmentsInboxApi } = useATRServiceApi();

  return useCallback(
    async (workflowId: string): Promise<AssessmentRole> => {
      try {
        const assessmentRole =
          await endOfYearAssessmentsInboxApi.v1AssessmentsEndOfYearWorkflowsWorkflowIdEmployeesMeRenderingGet(
            {
              workflowId
            }
          );

        return transformAssessmentRole(assessmentRole);
      } catch (error) {
        logger.error(
          'Failed to load v1AssessmentsEndOfYearWorkflowsWorkflowIdEmployeesMeRenderingGet',
          error
        );

        throw error;
      }
    },
    [endOfYearAssessmentsInboxApi]
  );
}

export function useAssessmentRole(workflowId: string) {
  const getAssessmentRole = useGetAssessmentRole();

  return useQuery({
    queryKey: WORKFLOWS_REQUEST_KEYS.getAssessmentRole(workflowId),
    queryFn: () => getAssessmentRole(workflowId)
  });
}
