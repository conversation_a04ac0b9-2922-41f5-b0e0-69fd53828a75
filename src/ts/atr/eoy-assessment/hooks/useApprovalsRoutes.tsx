import { FC, lazy } from 'react';

import { ErrorScreen } from '@oh/components';

import { INonIndexRouteObject } from '@/contracts/router';

import { ATRUserAssessmentRole, useAssessmentRole } from '../domain/workflows';
import type {
  AssessmentApprovalsProps,
  EOYManagerAssessmentApprovalsProps
} from '../pages/EOYAssessmentApprovals';

export interface UseApprovalsRoutesProps {
  entityType?: string;
  entityId?: string;
  requestId?: string;
  isTracking: boolean;
}

const EOY_ASSESSMENT_TYPE = 'EOYAssessment';
const EOY_MODIFY_ASSESSMENT_TYPE = 'EOYModifyAssessment';

const EOYSelfAssessmentApprovals = lazy<FC<AssessmentApprovalsProps>>(
  async () => ({
    default: (await import('../pages/EOYAssessmentApprovals'))
      .EOYSelfAssessmentApprovalsPage
  })
);

const EOYSubordinateAssessmentApprovals = lazy<FC<AssessmentApprovalsProps>>(
  async () => ({
    default: (await import('../pages/EOYAssessmentApprovals'))
      .EOYSubordinateAssessmentApprovalsPage
  })
);

const EOYUnknownAssessmentApprovals = lazy<FC<AssessmentApprovalsProps>>(
  async () => ({
    default: (await import('../pages/EOYAssessmentApprovals'))
      .EOYUnknownAssessmentApprovalsPage
  })
);

const EOYManagerAssessmentApprovals = lazy<
  FC<EOYManagerAssessmentApprovalsProps>
>(async () => ({
  default: (await import('../pages/EOYAssessmentApprovals'))
    .EOYManagerAssessmentApprovalsPage
}));

interface RedirectProps {
  requestId: string;
  isTracking: boolean;
}

const EOYAssessmentRedirect: FC<RedirectProps> = ({
  requestId,
  isTracking
}) => {
  const {
    data: assessmentRole,
    isPending,
    isError
  } = useAssessmentRole(requestId);
  // HERE

  if (isPending) {
    return null;
  }

  if (isError) {
    return <ErrorScreen className="!h-auto !bg-red-500" />;
  }

  const props: AssessmentApprovalsProps = {
    isTracking,
    requestId,
    performanceCycleName: assessmentRole.performanceCycleName,
    renderingType: assessmentRole.renderingType,
    assessmentSections: assessmentRole.assessmentSections
  };

  switch (assessmentRole.userAssessmentRole) {
    case ATRUserAssessmentRole.Employee:
      return <EOYSelfAssessmentApprovals {...props} />;

    case ATRUserAssessmentRole.Manager:
      return <EOYSubordinateAssessmentApprovals {...props} />;

    case ATRUserAssessmentRole.None:
      return <EOYUnknownAssessmentApprovals {...props} />;

    default:
      return <ErrorScreen className="!h-auto !bg-purple-700" />;
  }
};

export function useApprovalsRoutes({
  requestId,
  entityType,
  isTracking
}: UseApprovalsRoutesProps) {
  if (!requestId || !entityType) {
    return unknownRoutes;
  }

  if (entityType === EOY_ASSESSMENT_TYPE) {
    return getEOYAssessmentApprovalsRoutes(requestId, isTracking);
  }

  if (entityType === EOY_MODIFY_ASSESSMENT_TYPE) {
    return [
      {
        path: '/',
        element: (
          <EOYModifyAssessmentRedirect
            requestId={requestId}
            isTracking={isTracking}
          />
        )
      },
      errorRoute
    ];
  }

  return unknownRoutes;
}

const getEOYAssessmentApprovalsRoutes = (
  requestId: string,
  isTracking: boolean
): INonIndexRouteObject[] => [
  {
    path: '/',
    element: (
      <EOYAssessmentRedirect requestId={requestId} isTracking={isTracking} />
    )
  },
  errorRoute
];

const errorRoute: INonIndexRouteObject = {
  path: '*',
  element: <ErrorScreen className="!h-auto !bg-blue-900" />
};

const unknownRoutes: INonIndexRouteObject[] = [
  {
    path: '/',
    element: <ErrorScreen className="!h-auto !bg-green-300" />
  }
];

const EOYModifyAssessmentRedirect: FC<RedirectProps> = ({
  requestId,
  isTracking
}) => {
  const {
    data: assessmentRole,
    isPending,
    isError
  } = useAssessmentRole(requestId);
  // HERE
  if (isPending) {
    return null;
  }

  if (isError) {
    return <ErrorScreen className="!h-auto !bg-yellow-500" />;
  }

  const props: EOYManagerAssessmentApprovalsProps = {
    isTracking,
    requestId,
    performanceCycleName: assessmentRole.performanceCycleName,
    renderingType: assessmentRole.renderingType
  };

  return <EOYManagerAssessmentApprovals {...props} />;
};
