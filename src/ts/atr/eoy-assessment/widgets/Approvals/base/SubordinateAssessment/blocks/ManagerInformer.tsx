import { FC } from 'react';

import { DateUtils } from '@/atr/eoy-assessment/utils';
import { Informer as InformerComponent, InformerVariant } from '@/components';

export interface ManagerInformerProps {
  dueDate: Date | undefined;
}

export const ManagerInformer: FC<ManagerInformerProps> = ({ dueDate }) => {
  return <ManagerInformerContent dueDate={dueDate} />;
};

interface ManagerInformerContentProps {
  dueDate: Date | undefined;
}

const ManagerInformerContent: FC<ManagerInformerContentProps> = ({
  dueDate
}) => {
  return (
    <InformerComponent
      variant={InformerVariant.Warning}
      title="ATR Completion Reminder 1"
      description={`Please complete the Annual Review (ATR) by ${DateUtils.formatDate(
        dueDate
      )} and make the necessary Talent Journey updates.`}
    />
  );
};
