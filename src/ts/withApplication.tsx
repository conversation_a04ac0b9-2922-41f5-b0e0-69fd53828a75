import { FC } from 'react';

import { ToastProvider } from '@ot/onetalent-ui-kit';

import type { FederationApp } from '@oh/contracts';

import { appContainerClass, appRootModalsClass } from '@/constants';
import { ModalsProvider } from '@/providers/ModalsProvider';

import { AuthProvider } from './modules/auth';
import {
  AppProvider,
  AppConfigProvider,
  AppRouterProvider,
  AppInsightsProvider,
  AppQueryClientProvider
} from './providers';
import { OneHubFederatedApplicationProps } from './types';

import '@oh/components/dist/styles.css';
import '../scss/reset.scss';
import '../scss/ignore-isolation.scss';

export type FederationAppProps = FederationApp;

export const withApplication =
  (
    WrappedComponent: FC<OneHubFederatedApplicationProps>
  ): FC<FederationAppProps> =>
  (props) => (
    <ModalsProvider>
      <div className={appContainerClass}>
        <AppConfigProvider>
          <AppInsightsProvider>
            <AppQueryClientProvider>
              <AppProvider {...props}>
                <AuthProvider>
                  <ToastProvider>
                    <AppRouterProvider>
                      <WrappedComponent {...props} />
                    </AppRouterProvider>
                  </ToastProvider>
                </AuthProvider>
              </AppProvider>
            </AppQueryClientProvider>
          </AppInsightsProvider>
        </AppConfigProvider>
        <div className={appRootModalsClass} />
      </div>
    </ModalsProvider>
  );
