import { ReactNode } from 'react';

import clsx from 'clsx';

import { Icon, IconName } from '@/components/icons';
import { Text } from '@/components/text';

export enum InformerVariant {
  Info,
  Success,
  Warning,
  Error,
  Generic
}

interface InformerProps {
  className?: string;
  titleClassName?: string;
  descriptionClassName?: string;
  iconClassName?: string;
  title?: ReactNode;
  description?: ReactNode;
  variant?: InformerVariant;
  leftItem?: ReactNode;
  rightItem?: ReactNode;
}

const getIconName = (variant: InformerVariant): IconName => {
  switch (variant) {
    case InformerVariant.Success:
      return 'success';
    case InformerVariant.Error:
      return 'error';
    case InformerVariant.Warning:
      return 'warning';
    default:
      return 'info';
  }
};

const InformerVariantBG = {
  [InformerVariant.Success]:
    'border-informer-success-stroke bg-surface-success',
  [InformerVariant.Error]: 'border-informer-error-stroke bg-surface-error',
  [InformerVariant.Warning]:
    'border-informer-warning-stroke bg-surface-warning',
  [InformerVariant.Info]: 'border-informer-info-stroke bg-surface-info',
  [InformerVariant.Generic]:
    'border-informer-generic-stroke bg-informer-generic-fill'
};
const InformerVariantIconStyle = {
  [InformerVariant.Success]: 'text-informer-success-icon',
  [InformerVariant.Error]: 'text-informer-error-icon',
  [InformerVariant.Warning]: 'text-informer-warning-icon',
  [InformerVariant.Info]: 'text-informer-info-icon rotate-180',
  [InformerVariant.Generic]: 'text-informer-generic-icon rotate-180'
};

export const Informer = ({
  className,
  titleClassName,
  descriptionClassName,
  iconClassName,
  title,
  description,
  variant = InformerVariant.Info,
  leftItem,
  rightItem
}: InformerProps) => {
  const iconName = getIconName(variant);

  return (
    <div
      data-attributes="Informer"
      className={clsx(
        'flex gap-x-8 rounded-xl border border-solid p-16',
        InformerVariantBG[variant],
        className
      )}
      data-testid="informer-container"
    >
      {leftItem ? (
        <div className="mr-4 self-center">{leftItem}</div>
      ) : (
        <Icon
          name={iconName}
          className={clsx(
            'min-w-20',
            InformerVariantIconStyle[variant],
            iconClassName
          )}
        />
      )}

      <div className="flex flex-auto flex-col gap-y-4">
        {title && (
          <Text
            className={clsx(
              'text-body-2-medium text-text-heading',
              titleClassName
            )}
          >
            {title}
          </Text>
        )}
        {description && (
          <Text
            className={clsx(
              'text-body-2-regular text-text-body',
              descriptionClassName
            )}
          >
            {description}
          </Text>
        )}
      </div>

      {rightItem ? <div className="ml-4 self-center">{rightItem}</div> : null}
    </div>
  );
};
