using Asp.Versioning.Builder;
using OneTalent.WebAPI.Extensions;
using System.Net.Mime;

namespace OneTalent.FeedbackService.WebAPI.Endpoints.Surveys.Queries;

public static class EndpointsRegistrations
{
    public static void RegisterSurveysQueryEndpoints(this WebApplication app, ApiVersionSet versionSet)
    {
        var group = app.MapGroup($"{ApiVersions.ApiPrefix}")
            .WithTags("Surveys")
            .WithApiVersionSet(versionSet)
            .RequireAuthorization(Authorization.Constants.AclPolicy);

        #region Version 1.0

        group
            .MapGet("employees/me/surveys", GetMyDueSurveysEndpoint.ExecuteAsync)
            .Produces<List<string>>(contentType: MediaTypeNames.Application.Json)
            .MapToApiVersion(ApiVersions.V1);

        #endregion
    }
}
