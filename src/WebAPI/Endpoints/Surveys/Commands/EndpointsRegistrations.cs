using Asp.Versioning.Builder;
using OneTalent.WebAPI.Extensions;

namespace OneTalent.FeedbackService.WebAPI.Endpoints.Surveys.Commands;

public static class EndpointsRegistrations
{
    public static void RegisterSurveysCommandEndpoints(this WebApplication app, ApiVersionSet versionSet)
    {
        var group = app.MapGroup($"{ApiVersions.ApiPrefix}")
            .WithTags("Surveys")
            .WithApiVersionSet(versionSet)
            .RequireAuthorization(Authorization.Constants.AclPolicy);

        #region Version 1.0

        group.MapPost("employees/me/surveys/feedback-request",
                SubmitRequestedFeedbackSurveyEndpoint.ExecuteAsync)
            .Produces(StatusCodes.Status200OK)
            .MapToApiVersion(ApiVersions.V1);

        #endregion
    }
}
