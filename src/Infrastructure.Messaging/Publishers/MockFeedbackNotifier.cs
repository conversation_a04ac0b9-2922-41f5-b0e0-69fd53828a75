using OneTalent.Common.Extensions.ItemId;
using OneTalent.FeedbackService.Application.Messaging;

namespace OneTalent.FeedbackService.Infrastructure.Messaging.Publishers;

public sealed class MockFeedbackNotifier : IFeedbackNotifier
{
    public Task NotifyAsync(ItemId id, IFeedbackNotifier.Operation operation, CancellationToken cancellationToken)
    {
        return operation switch
        {
            IFeedbackNotifier.Operation.Created => Task.CompletedTask,
            IFeedbackNotifier.Operation.Updated => Task.CompletedTask,
            IFeedbackNotifier.Operation.Deleted => Task.CompletedTask,
            _ => throw new ArgumentOutOfRangeException(nameof(operation), operation, null)
        };
    }
}
