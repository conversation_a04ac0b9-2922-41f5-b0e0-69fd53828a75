import { TagVariant } from '@ot/onetalent-ui-kit';

import { AtrFormMajorStatuses } from '@/atr/domain';

export const getTagVariantByMajorStatus = (majorStatus: string): TagVariant => {
  switch (majorStatus) {
    case AtrFormMajorStatuses.SelfAssessment:
      return TagVariant.LightBlue;
    case AtrFormMajorStatuses.ManagerAssessment:
      return TagVariant.Supernova;
    case AtrFormMajorStatuses.DottedLineManagerEndorsement:
      return TagVariant.Electric;
    case AtrFormMajorStatuses.RatingApproval:
      return TagVariant.PacificBlue;
    case AtrFormMajorStatuses.Completed:
      return TagVariant.Green;
    default:
      return TagVariant.Grey;
  }
};
