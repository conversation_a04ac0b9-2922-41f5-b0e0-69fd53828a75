import { ToastVariant, useToast } from '@ot/onetalent-ui-kit';
import { useMutation, useQueryClient } from '@tanstack/react-query';

import { useAtrAdminServiceApi } from '@/api';
import { logger } from '@/shared/logger';

import { PERFORMANCE_FORMS_REQUEST_KEYS } from '../performanceFormsRequestKeys';

export interface DeletePerformanceFormParams {
  performanceFormId: string;
  reason: string;
}

export interface UseDeletePerformanceFormOptions {
  onSuccess?: () => void;
  onError?: () => void;
}

export const useDeletePerformanceForm = (
  options: UseDeletePerformanceFormOptions = {}
) => {
  const { atrPerformanceFormsApi } = useAtrAdminServiceApi();
  const queryClient = useQueryClient();
  const { addToast } = useToast();
  const { onSuccess, onError } = options;

  return useMutation({
    mutationFn: async ({
      performanceFormId,
      reason
    }: DeletePerformanceFormParams) => {
      try {
        return await atrPerformanceFormsApi.atrAdminV1PerformanceFormsPerformanceFormIdDelete(
          {
            performanceFormId,
            deletePerformanceFormRequest: { reason }
          }
        );
      } catch (error) {
        logger.error(
          'Failed to load atrAdminV1PerformanceFormsPerformanceFormIdDelete',
          error
        );
        throw error;
      }
    },
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: PERFORMANCE_FORMS_REQUEST_KEYS.all()
      });

      addToast({
        variant: ToastVariant.Success,
        title: 'Form was successfully deleted'
      });

      onSuccess?.();
    },
    onError: () => {
      addToast({
        variant: ToastVariant.Error,
        title: 'An error occurred, form was not deleted'
      });

      onError?.();
    }
  });
};
