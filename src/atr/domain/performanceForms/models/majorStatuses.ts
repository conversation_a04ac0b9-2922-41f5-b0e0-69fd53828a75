// TODO: remove this once the BE returns an enum instead of a string for “majorStatus”
export enum AtrFormMajorStatuses {
  Draft = 'Draft',
  SelfAssessment = 'Self Assessment',
  ManagerAssessment = 'Manager Assessment',
  DottedLineManagerEndorsement = 'Dotted Line Manager Endorsement',
  SecondLineManagerEndorsement = 'Second Line Manager Endorsement',
  Normalization = 'Normalization',
  RatingApproval = 'Rating Approval',
  RatingAnnouncement = 'Rating Announcement',
  Completed = 'Completed'
}

export const ATR_FORM_DELETABLE_MAJOR_STATUSES: string[] = [
  AtrFormMajorStatuses.Draft,
  AtrFormMajorStatuses.SelfAssessment,
  AtrFormMajorStatuses.ManagerAssessment,
  AtrFormMajorStatuses.DottedLineManagerEndorsement,
  AtrFormMajorStatuses.SecondLineManagerEndorsement,
  AtrFormMajorStatuses.Normalization
];
