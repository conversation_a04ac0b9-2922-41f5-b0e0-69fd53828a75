/* tslint:disable */
/* eslint-disable */
/**
 * OneTalent.AdminService - PublicAPI
 * PublicAPI documentation
 *
 * The version of the OpenAPI document: 1.0.162
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


import * as runtime from '../runtime';
import type {
  AtrCycleDto,
  DeletePerformanceFormRequest,
  GetPerformanceFormHistoryRequest,
  InsightsDto,
  NamedOptionsDto,
  PerformanceFormDtoPagedListResult,
  PerformanceFormHistoryDtoPagedListResult,
  PerformanceFormSearchQuery,
  PerformanceFormStatusDto,
  ProblemDetails,
} from '../models/index';
import {
    AtrCycleDtoFromJSON,
    AtrCycleDtoToJSON,
    DeletePerformanceFormRequestFromJSON,
    DeletePerformanceFormRequestToJSON,
    GetPerformanceFormHistoryRequestFromJSON,
    GetPerformanceFormHistoryRequestToJSON,
    InsightsDtoFromJSON,
    InsightsDtoToJSON,
    NamedOptionsDtoFromJSON,
    NamedOptionsDtoToJSON,
    PerformanceFormDtoPagedListResultFromJSON,
    PerformanceFormDtoPagedListResultToJSON,
    PerformanceFormHistoryDtoPagedListResultFromJSON,
    PerformanceFormHistoryDtoPagedListResultToJSON,
    PerformanceFormSearchQueryFromJSON,
    PerformanceFormSearchQueryToJSON,
    PerformanceFormStatusDtoFromJSON,
    PerformanceFormStatusDtoToJSON,
    ProblemDetailsFromJSON,
    ProblemDetailsToJSON,
} from '../models/index';

export interface AtrAdminV1PerformanceFormsAtrGroupsGetRequest {
    search?: string;
}

export interface AtrAdminV1PerformanceFormsB2bManagersGetRequest {
    search?: string;
}

export interface AtrAdminV1PerformanceFormsCompaniesGetRequest {
    search?: string;
}

export interface AtrAdminV1PerformanceFormsDirectoratesGetRequest {
    search?: string;
}

export interface AtrAdminV1PerformanceFormsDivisionsGetRequest {
    search?: string;
}

export interface AtrAdminV1PerformanceFormsDottedLineManagersGetRequest {
    search?: string;
}

export interface AtrAdminV1PerformanceFormsEmployeesGetRequest {
    search?: string;
}

export interface AtrAdminV1PerformanceFormsFunctionsGetRequest {
    search?: string;
}

export interface AtrAdminV1PerformanceFormsLineManagersGetRequest {
    search?: string;
}

export interface AtrAdminV1PerformanceFormsPerformanceFormIdDeleteRequest {
    performanceFormId: string;
    deletePerformanceFormRequest: DeletePerformanceFormRequest;
}

export interface AtrAdminV1PerformanceFormsPerformanceFormIdHistoryPostRequest {
    performanceFormId: string;
    getPerformanceFormHistoryRequest: GetPerformanceFormHistoryRequest;
}

export interface AtrAdminV1PerformanceFormsSearchPostRequest {
    performanceFormSearchQuery: PerformanceFormSearchQuery;
}

export interface AtrAdminV1PerformanceFormsTemplatesGetRequest {
    search?: string;
}

/**
 * 
 */
export class AtrPerformanceFormsApi extends runtime.BaseAPI {

    /**
     */
    async atrAdminV1PerformanceFormsAtrCyclesGetRaw(initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<runtime.ApiResponse<Array<AtrCycleDto>>> {
        const queryParameters: any = {};

        const headerParameters: runtime.HTTPHeaders = {};

        if (this.configuration && this.configuration.apiKey) {
            headerParameters["Authorization"] = await this.configuration.apiKey("Authorization"); // oauth2 authentication
        }

        const response = await this.request({
            path: `/atr-admin/v1/performance-forms/atr-cycles`,
            method: 'GET',
            headers: headerParameters,
            query: queryParameters,
        }, initOverrides);

        return new runtime.JSONApiResponse(response, (jsonValue) => jsonValue.map(AtrCycleDtoFromJSON));
    }

    /**
     */
    async atrAdminV1PerformanceFormsAtrCyclesGet(initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<Array<AtrCycleDto>> {
        const response = await this.atrAdminV1PerformanceFormsAtrCyclesGetRaw(initOverrides);
        return await response.value();
    }

    /**
     */
    async atrAdminV1PerformanceFormsAtrGroupsGetRaw(requestParameters: AtrAdminV1PerformanceFormsAtrGroupsGetRequest, initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<runtime.ApiResponse<Array<NamedOptionsDto>>> {
        const queryParameters: any = {};

        if (requestParameters['search'] != null) {
            queryParameters['search'] = requestParameters['search'];
        }

        const headerParameters: runtime.HTTPHeaders = {};

        if (this.configuration && this.configuration.apiKey) {
            headerParameters["Authorization"] = await this.configuration.apiKey("Authorization"); // oauth2 authentication
        }

        const response = await this.request({
            path: `/atr-admin/v1/performance-forms/atr-groups`,
            method: 'GET',
            headers: headerParameters,
            query: queryParameters,
        }, initOverrides);

        return new runtime.JSONApiResponse(response, (jsonValue) => jsonValue.map(NamedOptionsDtoFromJSON));
    }

    /**
     */
    async atrAdminV1PerformanceFormsAtrGroupsGet(requestParameters: AtrAdminV1PerformanceFormsAtrGroupsGetRequest = {}, initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<Array<NamedOptionsDto>> {
        const response = await this.atrAdminV1PerformanceFormsAtrGroupsGetRaw(requestParameters, initOverrides);
        return await response.value();
    }

    /**
     */
    async atrAdminV1PerformanceFormsB2bManagersGetRaw(requestParameters: AtrAdminV1PerformanceFormsB2bManagersGetRequest, initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<runtime.ApiResponse<Array<NamedOptionsDto>>> {
        const queryParameters: any = {};

        if (requestParameters['search'] != null) {
            queryParameters['search'] = requestParameters['search'];
        }

        const headerParameters: runtime.HTTPHeaders = {};

        if (this.configuration && this.configuration.apiKey) {
            headerParameters["Authorization"] = await this.configuration.apiKey("Authorization"); // oauth2 authentication
        }

        const response = await this.request({
            path: `/atr-admin/v1/performance-forms/b2b-managers`,
            method: 'GET',
            headers: headerParameters,
            query: queryParameters,
        }, initOverrides);

        return new runtime.JSONApiResponse(response, (jsonValue) => jsonValue.map(NamedOptionsDtoFromJSON));
    }

    /**
     */
    async atrAdminV1PerformanceFormsB2bManagersGet(requestParameters: AtrAdminV1PerformanceFormsB2bManagersGetRequest = {}, initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<Array<NamedOptionsDto>> {
        const response = await this.atrAdminV1PerformanceFormsB2bManagersGetRaw(requestParameters, initOverrides);
        return await response.value();
    }

    /**
     */
    async atrAdminV1PerformanceFormsCompaniesGetRaw(requestParameters: AtrAdminV1PerformanceFormsCompaniesGetRequest, initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<runtime.ApiResponse<Array<NamedOptionsDto>>> {
        const queryParameters: any = {};

        if (requestParameters['search'] != null) {
            queryParameters['search'] = requestParameters['search'];
        }

        const headerParameters: runtime.HTTPHeaders = {};

        if (this.configuration && this.configuration.apiKey) {
            headerParameters["Authorization"] = await this.configuration.apiKey("Authorization"); // oauth2 authentication
        }

        const response = await this.request({
            path: `/atr-admin/v1/performance-forms/companies`,
            method: 'GET',
            headers: headerParameters,
            query: queryParameters,
        }, initOverrides);

        return new runtime.JSONApiResponse(response, (jsonValue) => jsonValue.map(NamedOptionsDtoFromJSON));
    }

    /**
     */
    async atrAdminV1PerformanceFormsCompaniesGet(requestParameters: AtrAdminV1PerformanceFormsCompaniesGetRequest = {}, initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<Array<NamedOptionsDto>> {
        const response = await this.atrAdminV1PerformanceFormsCompaniesGetRaw(requestParameters, initOverrides);
        return await response.value();
    }

    /**
     */
    async atrAdminV1PerformanceFormsDirectoratesGetRaw(requestParameters: AtrAdminV1PerformanceFormsDirectoratesGetRequest, initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<runtime.ApiResponse<Array<NamedOptionsDto>>> {
        const queryParameters: any = {};

        if (requestParameters['search'] != null) {
            queryParameters['search'] = requestParameters['search'];
        }

        const headerParameters: runtime.HTTPHeaders = {};

        if (this.configuration && this.configuration.apiKey) {
            headerParameters["Authorization"] = await this.configuration.apiKey("Authorization"); // oauth2 authentication
        }

        const response = await this.request({
            path: `/atr-admin/v1/performance-forms/directorates`,
            method: 'GET',
            headers: headerParameters,
            query: queryParameters,
        }, initOverrides);

        return new runtime.JSONApiResponse(response, (jsonValue) => jsonValue.map(NamedOptionsDtoFromJSON));
    }

    /**
     */
    async atrAdminV1PerformanceFormsDirectoratesGet(requestParameters: AtrAdminV1PerformanceFormsDirectoratesGetRequest = {}, initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<Array<NamedOptionsDto>> {
        const response = await this.atrAdminV1PerformanceFormsDirectoratesGetRaw(requestParameters, initOverrides);
        return await response.value();
    }

    /**
     */
    async atrAdminV1PerformanceFormsDivisionsGetRaw(requestParameters: AtrAdminV1PerformanceFormsDivisionsGetRequest, initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<runtime.ApiResponse<Array<NamedOptionsDto>>> {
        const queryParameters: any = {};

        if (requestParameters['search'] != null) {
            queryParameters['search'] = requestParameters['search'];
        }

        const headerParameters: runtime.HTTPHeaders = {};

        if (this.configuration && this.configuration.apiKey) {
            headerParameters["Authorization"] = await this.configuration.apiKey("Authorization"); // oauth2 authentication
        }

        const response = await this.request({
            path: `/atr-admin/v1/performance-forms/divisions`,
            method: 'GET',
            headers: headerParameters,
            query: queryParameters,
        }, initOverrides);

        return new runtime.JSONApiResponse(response, (jsonValue) => jsonValue.map(NamedOptionsDtoFromJSON));
    }

    /**
     */
    async atrAdminV1PerformanceFormsDivisionsGet(requestParameters: AtrAdminV1PerformanceFormsDivisionsGetRequest = {}, initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<Array<NamedOptionsDto>> {
        const response = await this.atrAdminV1PerformanceFormsDivisionsGetRaw(requestParameters, initOverrides);
        return await response.value();
    }

    /**
     */
    async atrAdminV1PerformanceFormsDottedLineManagersGetRaw(requestParameters: AtrAdminV1PerformanceFormsDottedLineManagersGetRequest, initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<runtime.ApiResponse<Array<NamedOptionsDto>>> {
        const queryParameters: any = {};

        if (requestParameters['search'] != null) {
            queryParameters['search'] = requestParameters['search'];
        }

        const headerParameters: runtime.HTTPHeaders = {};

        if (this.configuration && this.configuration.apiKey) {
            headerParameters["Authorization"] = await this.configuration.apiKey("Authorization"); // oauth2 authentication
        }

        const response = await this.request({
            path: `/atr-admin/v1/performance-forms/dotted-line-managers`,
            method: 'GET',
            headers: headerParameters,
            query: queryParameters,
        }, initOverrides);

        return new runtime.JSONApiResponse(response, (jsonValue) => jsonValue.map(NamedOptionsDtoFromJSON));
    }

    /**
     */
    async atrAdminV1PerformanceFormsDottedLineManagersGet(requestParameters: AtrAdminV1PerformanceFormsDottedLineManagersGetRequest = {}, initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<Array<NamedOptionsDto>> {
        const response = await this.atrAdminV1PerformanceFormsDottedLineManagersGetRaw(requestParameters, initOverrides);
        return await response.value();
    }

    /**
     */
    async atrAdminV1PerformanceFormsEmployeesGetRaw(requestParameters: AtrAdminV1PerformanceFormsEmployeesGetRequest, initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<runtime.ApiResponse<Array<NamedOptionsDto>>> {
        const queryParameters: any = {};

        if (requestParameters['search'] != null) {
            queryParameters['search'] = requestParameters['search'];
        }

        const headerParameters: runtime.HTTPHeaders = {};

        if (this.configuration && this.configuration.apiKey) {
            headerParameters["Authorization"] = await this.configuration.apiKey("Authorization"); // oauth2 authentication
        }

        const response = await this.request({
            path: `/atr-admin/v1/performance-forms/employees`,
            method: 'GET',
            headers: headerParameters,
            query: queryParameters,
        }, initOverrides);

        return new runtime.JSONApiResponse(response, (jsonValue) => jsonValue.map(NamedOptionsDtoFromJSON));
    }

    /**
     */
    async atrAdminV1PerformanceFormsEmployeesGet(requestParameters: AtrAdminV1PerformanceFormsEmployeesGetRequest = {}, initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<Array<NamedOptionsDto>> {
        const response = await this.atrAdminV1PerformanceFormsEmployeesGetRaw(requestParameters, initOverrides);
        return await response.value();
    }

    /**
     */
    async atrAdminV1PerformanceFormsFunctionsGetRaw(requestParameters: AtrAdminV1PerformanceFormsFunctionsGetRequest, initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<runtime.ApiResponse<Array<NamedOptionsDto>>> {
        const queryParameters: any = {};

        if (requestParameters['search'] != null) {
            queryParameters['search'] = requestParameters['search'];
        }

        const headerParameters: runtime.HTTPHeaders = {};

        if (this.configuration && this.configuration.apiKey) {
            headerParameters["Authorization"] = await this.configuration.apiKey("Authorization"); // oauth2 authentication
        }

        const response = await this.request({
            path: `/atr-admin/v1/performance-forms/functions`,
            method: 'GET',
            headers: headerParameters,
            query: queryParameters,
        }, initOverrides);

        return new runtime.JSONApiResponse(response, (jsonValue) => jsonValue.map(NamedOptionsDtoFromJSON));
    }

    /**
     */
    async atrAdminV1PerformanceFormsFunctionsGet(requestParameters: AtrAdminV1PerformanceFormsFunctionsGetRequest = {}, initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<Array<NamedOptionsDto>> {
        const response = await this.atrAdminV1PerformanceFormsFunctionsGetRaw(requestParameters, initOverrides);
        return await response.value();
    }

    /**
     */
    async atrAdminV1PerformanceFormsInsightsGetRaw(initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<runtime.ApiResponse<InsightsDto>> {
        const queryParameters: any = {};

        const headerParameters: runtime.HTTPHeaders = {};

        if (this.configuration && this.configuration.apiKey) {
            headerParameters["Authorization"] = await this.configuration.apiKey("Authorization"); // oauth2 authentication
        }

        const response = await this.request({
            path: `/atr-admin/v1/performance-forms/insights`,
            method: 'GET',
            headers: headerParameters,
            query: queryParameters,
        }, initOverrides);

        return new runtime.JSONApiResponse(response, (jsonValue) => InsightsDtoFromJSON(jsonValue));
    }

    /**
     */
    async atrAdminV1PerformanceFormsInsightsGet(initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<InsightsDto> {
        const response = await this.atrAdminV1PerformanceFormsInsightsGetRaw(initOverrides);
        return await response.value();
    }

    /**
     */
    async atrAdminV1PerformanceFormsLineManagersGetRaw(requestParameters: AtrAdminV1PerformanceFormsLineManagersGetRequest, initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<runtime.ApiResponse<Array<NamedOptionsDto>>> {
        const queryParameters: any = {};

        if (requestParameters['search'] != null) {
            queryParameters['search'] = requestParameters['search'];
        }

        const headerParameters: runtime.HTTPHeaders = {};

        if (this.configuration && this.configuration.apiKey) {
            headerParameters["Authorization"] = await this.configuration.apiKey("Authorization"); // oauth2 authentication
        }

        const response = await this.request({
            path: `/atr-admin/v1/performance-forms/line-managers`,
            method: 'GET',
            headers: headerParameters,
            query: queryParameters,
        }, initOverrides);

        return new runtime.JSONApiResponse(response, (jsonValue) => jsonValue.map(NamedOptionsDtoFromJSON));
    }

    /**
     */
    async atrAdminV1PerformanceFormsLineManagersGet(requestParameters: AtrAdminV1PerformanceFormsLineManagersGetRequest = {}, initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<Array<NamedOptionsDto>> {
        const response = await this.atrAdminV1PerformanceFormsLineManagersGetRaw(requestParameters, initOverrides);
        return await response.value();
    }

    /**
     */
    async atrAdminV1PerformanceFormsPerformanceFormIdDeleteRaw(requestParameters: AtrAdminV1PerformanceFormsPerformanceFormIdDeleteRequest, initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<runtime.ApiResponse<void>> {
        if (requestParameters['performanceFormId'] == null) {
            throw new runtime.RequiredError(
                'performanceFormId',
                'Required parameter "performanceFormId" was null or undefined when calling atrAdminV1PerformanceFormsPerformanceFormIdDelete().'
            );
        }

        if (requestParameters['deletePerformanceFormRequest'] == null) {
            throw new runtime.RequiredError(
                'deletePerformanceFormRequest',
                'Required parameter "deletePerformanceFormRequest" was null or undefined when calling atrAdminV1PerformanceFormsPerformanceFormIdDelete().'
            );
        }

        const queryParameters: any = {};

        const headerParameters: runtime.HTTPHeaders = {};

        headerParameters['Content-Type'] = 'application/json';

        if (this.configuration && this.configuration.apiKey) {
            headerParameters["Authorization"] = await this.configuration.apiKey("Authorization"); // oauth2 authentication
        }

        const response = await this.request({
            path: `/atr-admin/v1/performance-forms/{performanceFormId}`.replace(`{${"performanceFormId"}}`, encodeURIComponent(String(requestParameters['performanceFormId']))),
            method: 'DELETE',
            headers: headerParameters,
            query: queryParameters,
            body: DeletePerformanceFormRequestToJSON(requestParameters['deletePerformanceFormRequest']),
        }, initOverrides);

        return new runtime.VoidApiResponse(response);
    }

    /**
     */
    async atrAdminV1PerformanceFormsPerformanceFormIdDelete(requestParameters: AtrAdminV1PerformanceFormsPerformanceFormIdDeleteRequest, initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<void> {
        await this.atrAdminV1PerformanceFormsPerformanceFormIdDeleteRaw(requestParameters, initOverrides);
    }

    /**
     */
    async atrAdminV1PerformanceFormsPerformanceFormIdHistoryPostRaw(requestParameters: AtrAdminV1PerformanceFormsPerformanceFormIdHistoryPostRequest, initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<runtime.ApiResponse<PerformanceFormHistoryDtoPagedListResult>> {
        if (requestParameters['performanceFormId'] == null) {
            throw new runtime.RequiredError(
                'performanceFormId',
                'Required parameter "performanceFormId" was null or undefined when calling atrAdminV1PerformanceFormsPerformanceFormIdHistoryPost().'
            );
        }

        if (requestParameters['getPerformanceFormHistoryRequest'] == null) {
            throw new runtime.RequiredError(
                'getPerformanceFormHistoryRequest',
                'Required parameter "getPerformanceFormHistoryRequest" was null or undefined when calling atrAdminV1PerformanceFormsPerformanceFormIdHistoryPost().'
            );
        }

        const queryParameters: any = {};

        const headerParameters: runtime.HTTPHeaders = {};

        headerParameters['Content-Type'] = 'application/json';

        if (this.configuration && this.configuration.apiKey) {
            headerParameters["Authorization"] = await this.configuration.apiKey("Authorization"); // oauth2 authentication
        }

        const response = await this.request({
            path: `/atr-admin/v1/performance-forms/{performanceFormId}/history`.replace(`{${"performanceFormId"}}`, encodeURIComponent(String(requestParameters['performanceFormId']))),
            method: 'POST',
            headers: headerParameters,
            query: queryParameters,
            body: GetPerformanceFormHistoryRequestToJSON(requestParameters['getPerformanceFormHistoryRequest']),
        }, initOverrides);

        return new runtime.JSONApiResponse(response, (jsonValue) => PerformanceFormHistoryDtoPagedListResultFromJSON(jsonValue));
    }

    /**
     */
    async atrAdminV1PerformanceFormsPerformanceFormIdHistoryPost(requestParameters: AtrAdminV1PerformanceFormsPerformanceFormIdHistoryPostRequest, initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<PerformanceFormHistoryDtoPagedListResult> {
        const response = await this.atrAdminV1PerformanceFormsPerformanceFormIdHistoryPostRaw(requestParameters, initOverrides);
        return await response.value();
    }

    /**
     */
    async atrAdminV1PerformanceFormsSearchPostRaw(requestParameters: AtrAdminV1PerformanceFormsSearchPostRequest, initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<runtime.ApiResponse<PerformanceFormDtoPagedListResult>> {
        if (requestParameters['performanceFormSearchQuery'] == null) {
            throw new runtime.RequiredError(
                'performanceFormSearchQuery',
                'Required parameter "performanceFormSearchQuery" was null or undefined when calling atrAdminV1PerformanceFormsSearchPost().'
            );
        }

        const queryParameters: any = {};

        const headerParameters: runtime.HTTPHeaders = {};

        headerParameters['Content-Type'] = 'application/json';

        if (this.configuration && this.configuration.apiKey) {
            headerParameters["Authorization"] = await this.configuration.apiKey("Authorization"); // oauth2 authentication
        }

        const response = await this.request({
            path: `/atr-admin/v1/performance-forms/search`,
            method: 'POST',
            headers: headerParameters,
            query: queryParameters,
            body: PerformanceFormSearchQueryToJSON(requestParameters['performanceFormSearchQuery']),
        }, initOverrides);

        return new runtime.JSONApiResponse(response, (jsonValue) => PerformanceFormDtoPagedListResultFromJSON(jsonValue));
    }

    /**
     */
    async atrAdminV1PerformanceFormsSearchPost(requestParameters: AtrAdminV1PerformanceFormsSearchPostRequest, initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<PerformanceFormDtoPagedListResult> {
        const response = await this.atrAdminV1PerformanceFormsSearchPostRaw(requestParameters, initOverrides);
        return await response.value();
    }

    /**
     */
    async atrAdminV1PerformanceFormsStatusesGetRaw(initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<runtime.ApiResponse<Array<PerformanceFormStatusDto>>> {
        const queryParameters: any = {};

        const headerParameters: runtime.HTTPHeaders = {};

        if (this.configuration && this.configuration.apiKey) {
            headerParameters["Authorization"] = await this.configuration.apiKey("Authorization"); // oauth2 authentication
        }

        const response = await this.request({
            path: `/atr-admin/v1/performance-forms/statuses`,
            method: 'GET',
            headers: headerParameters,
            query: queryParameters,
        }, initOverrides);

        return new runtime.JSONApiResponse(response, (jsonValue) => jsonValue.map(PerformanceFormStatusDtoFromJSON));
    }

    /**
     */
    async atrAdminV1PerformanceFormsStatusesGet(initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<Array<PerformanceFormStatusDto>> {
        const response = await this.atrAdminV1PerformanceFormsStatusesGetRaw(initOverrides);
        return await response.value();
    }

    /**
     */
    async atrAdminV1PerformanceFormsTemplatesGetRaw(requestParameters: AtrAdminV1PerformanceFormsTemplatesGetRequest, initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<runtime.ApiResponse<Array<NamedOptionsDto>>> {
        const queryParameters: any = {};

        if (requestParameters['search'] != null) {
            queryParameters['search'] = requestParameters['search'];
        }

        const headerParameters: runtime.HTTPHeaders = {};

        if (this.configuration && this.configuration.apiKey) {
            headerParameters["Authorization"] = await this.configuration.apiKey("Authorization"); // oauth2 authentication
        }

        const response = await this.request({
            path: `/atr-admin/v1/performance-forms/templates`,
            method: 'GET',
            headers: headerParameters,
            query: queryParameters,
        }, initOverrides);

        return new runtime.JSONApiResponse(response, (jsonValue) => jsonValue.map(NamedOptionsDtoFromJSON));
    }

    /**
     */
    async atrAdminV1PerformanceFormsTemplatesGet(requestParameters: AtrAdminV1PerformanceFormsTemplatesGetRequest = {}, initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<Array<NamedOptionsDto>> {
        const response = await this.atrAdminV1PerformanceFormsTemplatesGetRaw(requestParameters, initOverrides);
        return await response.value();
    }

}
