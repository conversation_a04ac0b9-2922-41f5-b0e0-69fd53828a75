from mitmproxy import http

from config import PORT, mf_hosts

def handle_request(flow: http.HTTPFlow) -> None:
    if flow.request.pretty_host not in mf_hosts:
        return

    if is_external_mf_module(flow):
        return

    flow.request.host = "localhost"
    flow.request.port = PORT
    flow.request.scheme = "http"

    handle_mf_path(flow, "/atr")

def handle_mf_path(flow, query_prefix):
    if is_mf_module(flow, query_prefix):
        flow.request.path = flow.request.path.replace(query_prefix, "", 1)

def is_mf_module(flow, query_prefix):
    if flow.request.path.startswith(query_prefix):
        return True
    return False

def is_external_mf_module(flow):
    if (
        is_mf_module(flow, "/query") or
        is_mf_module(flow, "/performance") or
        is_mf_module(flow, "/talent-journey") or
        is_mf_module(flow, "/feedback") or
        is_mf_module(flow, "/career-development") or
        is_mf_module(flow, "/career-aspiration")
    ):
        return True

    return False
