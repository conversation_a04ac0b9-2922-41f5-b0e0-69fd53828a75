from mitmproxy import http
import re

from config import api_hosts, headers

overrides = {
    "me:assessments-end-of-year:404": False,
    "me:assessments-end-of-year:500": False,
    "me:top-info:403:not-started": False,
    "me:top-info:403:finished": <PERSON>alse,
    "me:top-info:500": <PERSON>als<PERSON>,
    "manager:top-info:404": <PERSON>als<PERSON>,
    "manager:top-info:500": <PERSON>als<PERSON>,
    "manager:top-info:403:not-started": False,
    "manager:top-info:403:finished": False
}
#
def handle_mocked_response(flow: http.HTTPFlow) -> None:
    if flow.request.pretty_host not in api_hosts:
        return

    if flow.request.method == "OPTIONS":
        return

    if overrides["me:assessments-end-of-year:404"]:
        handle_me_assessments_end_of_year_404(flow)
        return

    if overrides["me:assessments-end-of-year:500"]:
        handle_me_assessments_end_of_year_500(flow)
        return

    if overrides["me:top-info:403:not-started"]:
        handle_me_top_info_403_not_started(flow)
        return

    if overrides["me:top-info:403:finished"]:
        handle_me_top_info_403_finished(flow)
        return

    if overrides["me:top-info:500"]:
        handle_me_top_info_500(flow)
        return

    if overrides["manager:top-info:404"]:
        handle_manager_top_info_404(flow)
        return

    if overrides["manager:top-info:500"]:
        handle_manager_top_info_500(flow)
        return

    if overrides["manager:top-info:403:not-started"]:
        handle_manager_top_info_403_not_started(flow)
        return

    if overrides["manager:top-info:403:finished"]:
        handle_manager_top_info_403_finished(flow)
        return

def handle_me_assessments_end_of_year_404(flow: http.HTTPFlow) -> None:
    if re.search(r"/employees/me/assessments-end-of-year", flow.request.path):
        flow.response = http.Response.make(
           404,
           b"{}",
           headers
        )


def handle_me_assessments_end_of_year_500(flow: http.HTTPFlow) -> None:
    if re.search(r"/employees/me/assessments-end-of-year", flow.request.path):
        flow.response = http.Response.make(
           500,
           b"{}",
           headers
        )

def handle_me_top_info_403_not_started(flow: http.HTTPFlow) -> None:
    if re.search(r"employees/assessments-end-of-year/.+/top-info", flow.request.path):
        flow.response = http.Response.make(
           403,
           b'{"Status": { "SelfAssessment": {"HasStarted": false, "HasFinished": false}}, "Reasons": ["You are not allowed to access this resource."]}',
           headers
        )

def handle_me_top_info_403_finished(flow: http.HTTPFlow) -> None:
    if re.search(r"employees/assessments-end-of-year/.+/top-info", flow.request.path):
        flow.response = http.Response.make(
           403,
           b'{"Status": { "SelfAssessment": {"HasStarted": true, "HasFinished": true}}, "Reasons": ["You are not allowed to access this resource."]}',
           headers
        )

def handle_me_top_info_500(flow: http.HTTPFlow) -> None:
    if re.search(r"employees/assessments-end-of-year/.+/top-info", flow.request.path):
        flow.response = http.Response.make(
           500,
           b"{}",
           headers
        )

def handle_manager_top_info_404(flow: http.HTTPFlow) -> None:
    if re.search(r"managers/me/assessments-end-of-year/.+/top-info", flow.request.path):
        flow.response = http.Response.make(
           404,
           b"{}",
           headers
        )

def handle_manager_top_info_500(flow: http.HTTPFlow) -> None:
    if re.search(r"managers/me/assessments-end-of-year/.+/top-info", flow.request.path):
        flow.response = http.Response.make(
           500,
           b"{}",
           headers
        )

def handle_manager_top_info_403_not_started(flow: http.HTTPFlow) -> None:
    if re.search(r"managers/me/assessments-end-of-year/.+/top-info", flow.request.path):
        flow.response = http.Response.make(
           403,
           b'{"Status": { "ManagerAssessment": {"HasStarted": false, "HasFinished": false}}, "Reason": "You are not allowed to access this resource."}',
           headers
        )

def handle_manager_top_info_403_finished(flow: http.HTTPFlow) -> None:
    if re.search(r"managers/me/assessments-end-of-year/.+/top-info", flow.request.path):
        flow.response = http.Response.make(
           403,
           b'{"Status": { "ManagerAssessment": {"HasStarted": true, "HasFinished": true}}, "Reason": "You are not allowed to access this resource."}',
           headers
        )

