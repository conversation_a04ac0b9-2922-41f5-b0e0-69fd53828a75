from mitmproxy import http
import re

from config import onehub_hosts

def handle_onetalent_response(flow: http.HTTPFlow) -> None:
    if flow.request.pretty_host != "localhost":
        return

    if not (flow.response and "text/html" in flow.response.headers.get("Content-Type", "")):
        return

    body = flow.response.text

    body = re.sub(r'src="(.*?)"', r'src="http://localhost:3007\1"', body)
    body = re.sub(r'from "(.*?)"', r'from "http://localhost:3007\1"', body)

    flow.response.text = body

def handle_onehub_response(flow: http.HTTPFlow) -> None:
    if flow.request.pretty_host not in onehub_hosts:
        return

    handle_security_headers(flow)

def handle_security_headers(flow: http.HTTPFlow) -> None:
    if not (flow.response and "text/html" in flow.response.headers.get("Content-Type", "")):
        return

    flow.response.headers.pop("content-security-policy", None)
