import { YAxisProps, XAxisProps } from 'recharts';

import { DataAttributesProps } from '@/components/base/types';

export type DataPoint = number;

export interface LineChartDataPoint {
  x: DataPoint;
  y: DataPoint;
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  [key: string]: any;
}

export interface LineChartSerie {
  id: string;
  data: LineChartDataPoint[];
  name: string;
  color: string;
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  [key: string]: any;
}

export interface LineChartSlice {
  id: string;
  data: LineChartDataPoint[];
}

export interface TooltipItem {
  dataKey: string;
  name: string; // Same as serie name
  value: DataPoint; // Slice Y value
  color: string; // Same as serie color
  payload: LineChartDataPoint;
}

export type TooltipFormatter = (value: TooltipItem) => React.ReactNode;

export type AxisFormatter = (value: DataPoint, index: number) => string;

export type OnSliceSelect = (slice: LineChartSlice | undefined) => void;

export interface LineChartProps extends DataAttributesProps {
  className?: string;
  series: LineChartSerie[];
  title?: React.ReactNode;
  initialSelectedSliceId?: string;
  selectedSliceId?: string;
  onSliceSelect?: OnSliceSelect;
  tooltipFormatter?: TooltipFormatter;
  xAxisSliceOffset?: number;
  xAxisDomain?: [string, string];
  yAxisDomain?: [number, number];
  width?: string | number;
  height?: string | number;
  yAxisConfig?: Omit<YAxisProps, 'domain'>;
  xAxisConfig?: Omit<XAxisProps, 'domain'>;
}

export interface SliceOverlayItem {
  slice: LineChartSlice;
  fromX: DataPoint;
  toX: DataPoint;
}
