import { FC } from 'react';

import { IllustrationMessage, IllustrationVariant } from '../Illustration';
import { DataAttributesProps } from '../types';

export type DropdownNoResultsFoundProps = DataAttributesProps & {
  className?: string;
};

export const DropdownNoResultsFound: FC<DropdownNoResultsFoundProps> = ({
  className,
  dataAttributes = 'DropdownNoResultsFound'
}) => {
  return (
    <div
      data-attributes={dataAttributes}
      className="flex min-h-[264px] w-full items-center justify-center md:!min-h-[248px]"
    >
      <IllustrationMessage
        className={className}
        illustrationVariant={IllustrationVariant.NothingFound}
        title="No results found"
        description={
          <p>
            No matches found.
            <br />
            Try adjusting your search or filters.
          </p>
        }
      />
    </div>
  );
};
