import { Change<PERSON>vent, DragEvent, useCallback, useState } from 'react';

import clsx from 'clsx';

import {
  UploadDragAndDropWrapper,
  UploadInput,
  UploadTitle,
  UploadedFileList
} from './components';
import {
  DEFAULT_ACCEPTED_FILE_TYPES,
  DEFAULT_ALL_FILES_SIZE,
  DEFAULT_DISABLED,
  DEFAULT_FILE_LIST,
  DEFAULT_FILE_SIZE,
  DEFAULT_MAX_NUMBER_FILES,
  DEFAULT_MULTIPLE,
  DEFAULT_TITLE_IS_ALWAYS_VISIBLE,
  DEFAULT_VIEW_ONLY
} from './constants';
import { useGlobalFileDrag, useUpload } from './hooks';
import { DefaultFileListInput, FileListInput, UploadEvents } from './types';

export interface UploadProps extends UploadEvents {
  /**
   * Additional class name for the upload component
   */
  className?: string;
  /**
   * Control whether upload functionality is disabled
   */
  disabled?: boolean;
  /**
   * If set, only displays the list of uploaded documents without the upload section.
   */
  viewOnly?: boolean;
  /**
   * If true - the group title is always visible
   */
  titleIsAlwaysVisible?: boolean;
  /**
   * Display title for the upload area
   */
  title: string;
  /**
   * Maximum number of files allowed to upload
   */
  maxCount?: number;
  /**
   * Accepted file types (MIME types or extensions)
   */
  acceptedFileTypes?: Array<string>;
  /**
   * Maximum file size in bytes
   */
  maxFileSize?: number;
  /**
   * Maximum total files size in bytes
   */
  maxAllFilesSize?: number;
  /**
   * Allow multiple file selection
   */
  multiple?: boolean;

  /**
   * Default list of files to show as uploaded
   */
  defaultFileList?: DefaultFileListInput;
  /**
   * Controlled list of files (for controlled component usage)
   */
  fileList?: DefaultFileListInput;
  /**
   * Flag indicating whether the download button should be disabled.
   */
  isDownloadBtnDisabled?: boolean;
  /**
   * Flag indicating whether the remove button should be disabled.
   */
  isRemoveBtnDisabled?: boolean;
  /**
   * Function to change the display of fileList
   */
  renderList?: (fileList: DefaultFileListInput) => FileListInput;
}

/**
 * Upload
 *
 * @returns {JSX.Element} The rendered Upload component.
 *
 * @example
 * ```tsx
 * <Upload
 *   title={'Some Title'}
 *   maxCount={1}
 *   acceptedFileTypes={['.pdf', '.xlsx', '.docs']}
 *   maxFileSize={5 * 1024 * 1024}
 *   multiple
 *   beforeUpload={(info) => {
 *     console.log('RateWorkObjectives: beforeUpload', info);
 *     return true;
 *   }}
 *   onChange={(info) => {
 *     console.log('RateWorkObjectives: onChange', info);
 *   }}
 *   onRemove={(info) => {
 *     console.log('RateWorkObjectives: onRemove', info);
 *     return true;
 *   }}
 *   onRetryUpload={(info) => {
 *     console.log('RateWorkObjectives: onRetryUpload', info);
 *     return true;
 *   }}
 *   onDownload={(info) => {
 *     console.log('RateWorkObjectives: onDownload', info);
 *   }}
 *   onDrop={(info) => {
 *     console.log('RateWorkObjectives: onDrop', info);
 *   }}
 * />
 * ```
 */
export const Upload: React.FC<UploadProps> = ({
  className,
  disabled = DEFAULT_DISABLED,
  viewOnly = DEFAULT_VIEW_ONLY,
  titleIsAlwaysVisible = DEFAULT_TITLE_IS_ALWAYS_VISIBLE,
  title,
  maxCount = DEFAULT_MAX_NUMBER_FILES,
  acceptedFileTypes = DEFAULT_ACCEPTED_FILE_TYPES,
  maxFileSize = DEFAULT_FILE_SIZE,
  maxAllFilesSize = DEFAULT_ALL_FILES_SIZE,
  multiple = DEFAULT_MULTIPLE,

  defaultFileList = DEFAULT_FILE_LIST,
  fileList,
  isDownloadBtnDisabled,
  isRemoveBtnDisabled,
  renderList,

  beforeUpload,
  onChange,
  onRemove,
  onRetryUpload,
  onDownload,
  onDrop,
  onNotify
}) => {
  const isDraggingFile = useGlobalFileDrag();
  const [isOver, setIsOver] = useState(false);

  const { internalFileList, handleChange, handleRemove, handleRetryUpload } =
    useUpload({
      disabled,
      maxCount,
      acceptedFileTypes,
      maxFileSize,
      maxAllFilesSize,

      defaultFileList,
      fileList,

      beforeUpload,
      onChange,
      onRemove,
      onRetryUpload,
      onNotify
    });

  const handleInputChange = useCallback(
    (e: ChangeEvent<HTMLInputElement>) => {
      handleChange(e.target.files, e);

      e.target.value = '';
    },
    [handleChange]
  );

  const handleDragAndDropChange = useCallback(
    (e: DragEvent<HTMLDivElement>) => {
      handleChange(e.dataTransfer.files, e);

      if (onDrop) {
        onDrop(e);
      }
    },
    [handleChange, onDrop]
  );

  const withFileList = Boolean(internalFileList.length);

  if (viewOnly && !internalFileList.length) return null;

  return (
    <div
      data-attributes="Upload"
      className={clsx(
        'flex flex-col overflow-hidden rounded-[12px] border border-solid border-divider-dark bg-surface-grey_0',
        {
          'h-[72px]': !withFileList,
          'bg-surface-grey_10': isOver
        },
        className
      )}
    >
      {!viewOnly ? (
        <UploadDragAndDropWrapper
          withFileList={withFileList}
          isDraggingFile={isDraggingFile}
          disabled={disabled}
          isOver={isOver}
          setIsOver={setIsOver}
          onChange={handleDragAndDropChange}
        >
          <UploadInput
            disabled={disabled}
            title={title}
            acceptedFileTypes={acceptedFileTypes}
            maxFileSize={maxFileSize}
            multiple={multiple}
            onChange={handleInputChange}
          />
        </UploadDragAndDropWrapper>
      ) : (
        <UploadTitle
          titleIsAlwaysVisible={titleIsAlwaysVisible}
          title={title}
        />
      )}

      <UploadedFileList
        fileList={internalFileList}
        viewOnly={viewOnly}
        isDownloadBtnDisabled={isDownloadBtnDisabled}
        isRemoveBtnDisabled={isRemoveBtnDisabled}
        renderList={renderList}
        onRemove={handleRemove}
        onRetryUpload={handleRetryUpload}
        onDownload={onDownload}
      />
    </div>
  );
};
