# Introduction

TODO: Give a short introduction of your project. Let this section explain the objectives or the motivation behind this project.

# Getting Started

TODO: Guide users through getting your code up and running on their own system. In this section you can talk about:

1. Installation process
2. Software dependencies
3. Latest releases
4. API references

# Installation (WIP)

## Pre-requisites

1. Install the `NodeJS` LTS from the [official](https://nodejs.org/en) webpage.

   > You may check minimal LTS version from `.nvmrc` in the root of the project

2. Install `yarn` by running:

```shell
npm install -g yarn
```

## Setup project for local development

1. Project relies on ADNOC npm private registries, first of all you need to [generate](https://docs.microsoft.com/en-us/azure/devops/organizations/accounts/use-personal-access-tokens-to-authenticate?view=azure-devops/) your Personal Access Token (PAT)

2. Then you need to apply your PAT in `.npmrc` config by running `setup` script in the root `package.json`.

> **Note 1** Do not alter local `.npmrc` file. It defines your registries necessary to get packages for the project

> **Note 2:** do not forget to remove your PAT after the successful node_modules installation.

> **Note 3:** The application implements authentication flow via the Microsoft Authentication Library. In order to make your app start and running you need to have a valid access token.

3. Run `yarn install` to install all dependencies.
4. Run `yarn start` to launch OneTalent application locally.

# Build and Test

TODO: Describe and show how to build your code and run the tests.

# Generate API

Auto generated API is used. API is generated using [OpenAPI Genearator](https://openapi-generator.tech/). Swagger files is provided by BE team and it is updated automatically during BE deployment.

## Pre-requisites

[Podman](https://podman.io/) should be installed and it should have a connection to the internet.

## How to re-generate API clients

1. Replace the `src/ts/services/queryService/swagger.json` file with the newest version (copying from dev env or local swagger).
2. Re-generate API.

```
yarn generate
```

## How to use generated API

The generated API can be imported only in the `src/ts/domainModel` folder. Please create your own interfaces for the models and hooks using [react-query](https://tanstack.com/query/latest).

## How to add reference on a new API service

You need to add a folder with the name of your service in the `src/ts/service` directory. Also add `generated/` and `index.ts` to the newly created folder.

```
.. src/ts/services/
.... ${your_service_name}
........ generated/
........ index.ts
```

And also update package.json scripts section and add `generate:${your_service_name}` command for a new service generation:

```

"generate:${your_service_name}": "podman run -v ./src/ts/services/${your_service_name}:/out -it --rm docker.io/openapitools/openapi-generator-cli generate -i /out/swagger.json -g typescript-fetch --additional-properties=modelPropertyNaming=original -o /out/generated  --skip-validate-spec"
```

Reference new command in the `generate` command:

```
  "generate": "yarn generate:query-service && ... && yarn generate:${your_service_name}",
```

Generate new api and add re-export generated code under a single namespace. Update `index.ts` file in the ${your_service_name} with the next statement:

```
export * as ${Your_Service_Name} from './generated';
```

# How to run back-end locally

## Pre-requisites

1. Install [Podman](https://podman.io/)
2. Checkout back-end source code https://devopsad.visualstudio.com/One%20Talent/_git/be-development.
3. Obtain Personal Access Token (please see Instalation section).

## Steps

1. Open be-development folder in terminal.
2. Go to the `be-development/onboarding` folder.
3. Run nuget-setup.(sh|cmd) script and provide your PAT after script promt.

```
$ ./nuget-setup.sh
This script will configure NuGet source to ADNOC

Now please open https://devopsad.visualstudio.com/_usersSettings/tokens and create Personal Access Token
Make sure Packaging read is included!
Enter Token:
```

### ---START--- BLOCK for MAC users on LOCAL

Before going to below steps make sure your Podman is initialized and running.

First of all make sure that docker compatibility is enable in Podman.

Open a command line and run below commands (first 2 might be unnecessary in case you are first time running it)

```
podman machine stop
podman machine rm podman-machine-default
podman machine init
podman machine start
```

Go to the `be-development/src/be-query-service/scripts/mac` folder in all `container-**` files change all `_command=docker` to `_command=podman`

Go to the `onboarding/nuget-setup.sh` file and change
`export ADNOC_FEED_TOKEN="{YOUR_PAT_TOKEN}"`

Go to the `src/be-query-service/compose.yml` file and add
`AzureAd__ClientSecret: "{ASK_TOKEN_FROM_TEAM}"`, after line `ConnectionStrings__OneTalentDb`

Seems before running 5th and 6th points we should also either add ADNOC_FEED_TOKEN as a global var or each time run
`export ADNOC_FEED_TOKEN="{YOUR_PAT_TOKEN}"`

### ---END--- BLOCK for MAC users on LOCAL

4. Go to the `be-development/src/be-query-service/scripts/` folder.
5. Run script ./(mac|win)/container-build.(sh|cmd).
6. Run script ./(mac|win)/container-run.(sh|cmd).
7. Back-end is available on http::/localhost:8080/.

```
$ ./container-run.sh
one-talent/be-query-service:0.0.1-beta will be used
Starting service. URL: http://localhost:8080/swagger
info: Microsoft.Hosting.Lifetime[14]
      Now listening on: http://[::]:8080
info: Microsoft.Hosting.Lifetime[0]
      Application started. Press Ctrl+C to shut down.
info: Microsoft.Hosting.Lifetime[0]
      Hosting environment: Development
info: Microsoft.Hosting.Lifetime[0]
      Content root path: /app
```

# How to run local microfrontend on OneHub stg

1. Install [mitmproxy](https://docs.mitmproxy.org/stable/overview-installation/).
2. Install [Google Chrome Canary](https://www.google.com/chrome/canary/)
3. Run app as microfrontend (atr, performance, admin, feedback, career-development, carrer-aspiration, etc.):

```sh
yarn start:mf:dev:{microfrontend}
```

> NOTE: For a different environment change `dev` to another environment available (stg, uat, etc.).
>
> But we **highly recommend** to stick with the `dev` for general tasks
> and `uat` for the `Inbox` (aka Approvals) tasks 4. Run FE proxy:

```sh
yarn proxy:fe
```

5. Launch Google Chrome **Canary**. It must be a fresh launch:

```sh
pkill -f "Google Chrome Canary"
open -a "Google Chrome Canary" --args --proxy-server="http://localhost:8081"
```

6. Check that [mitm page](https://mitm.it/) is available in Google Chrome Canary and install certificates provided by this page

> NOTE: Do not forget to enable **keychain access** for certificates installation.
> If necessary, repeat the previous step to double-check certificates application.

7. Open the corresponding environment page and your microfrontend should be working

# How to setup OneHub Approvals dev environment

## Prerequisites

1. Connect to [zscaller](https://confluence.adnoc.ae/display/OT/VPN+Access+to+Confluence+via+Zscaler) vpn.
2. Install [mitmproxy](https://docs.mitmproxy.org/stable/overview-installation/).
3. Start Chrome browser with the configured proxy.

```sh
${chrome_executable} --proxy-server="http://localhost:8081"
```

> **NOTE:** You can create a separate profile in Chrome and use it for work with OneTalent

```sh
${chrome_executable} --profile-directory="${profile_name}" --proxy-server="http://localhost:8081"

```

> **NOTE:** You can add the previous command to .bashrc or to .zshrc (depends on terminal you use)

```.bashrc
alias chrome='${chrome_executable} --profile-directory="${profile_name}" --proxy-server="http://localhost:8081"'
```

4. Install mitmproxy SSL certificates https://docs.mitmproxy.org/stable/concepts-certificates/.

## Start dev environment

1. Start FE for for local STG environment.

```sh
yarn start:mf:stg:{microfrontend}
```

> **NOTE:** Approvals are only available on STG environment.
> **NOTE:** app url is https://onehub2-stg.adnoc.ae/

2. Start proxy for FE

```sh
yarn proxy:fe
```

> **NOTE:** When proxy is started, https://stg.onetalent.adnoc.ae/one-talent/skills-management should be used instead of http://localhost:3006/ to access local standalone app.
> **NOTE:** In this case all API calls are served from real STG BE.

## How to work with remote BE (DEV or STG)

If you want to serve data from DEV or STG environment insted of loal BE you can use next combination of commands:

_DEV_ only standalone app

```sh
yarn start:dev
yarn proxy:fe

```

> **NOTE:** app url is https://dev.onetalent.adnoc.ae/one-talent/skills-management.

_DEV_ standalone app + OneHub integration

```sh
yarn start:mf:dev:{microfrontend}
yarn proxy:fe

```

> **NOTE:** app urls are https://dev.onetalent.adnoc.ae/one-talent/skills-management and https://onehub2-dev1.adnoc.ae/.
> **NOTE:** data will be served from the dev API.

# How to run app using Vite bundler

## MF mode

MF mode is supported by default by the vite bundler. You can run the app in the microfrontend mode by running:

```sh
yarn dev:dev
yarn dev:stg
yarn dev:uat
yarn dev:uat2
yarn dev:preview
```
NOTE: do not forget to run the proxy for FE before running the app:

```sh
yarn proxy:fe
```

## Standalone mode

1. Local BE API:
```sh
yarn dev
```

2. DEV BE API:
```sh
yarn dev:dev
```
The app is accessible by URL: `http://localhost:3007`
Also, it can be accessed by URL `https://dev.onetalent.adnoc.ae/` and requires a running proxy:
```sh
yarn proxy:fe
```

3. STG BE API:
```sh
yarn dev:stg
```
The app is only accessible by URL `https://stg.onetalent.adnoc.ae/` and requires a running proxy:
```sh
yarn proxy:fe
```

# How to create a Skill Approval

Open swagger on dev or stg environment and create request via swagger (https://api.dev.onetalent.adnoc.ae/swagger/index.html#/Workflow/post_v1_workflow_job_description_approval).

# How to update generated styles

1. Get design tokens from figma.

- Open ADNOC - Design System https://www.figma.com/design/5F41VDpKqfwo8SwjQSAld2/ADNOC-%E2%80%93-Design-System
- If you don't have Dev Mode permissions you can copy design file via "Duplicate to your drafts" action from context menu.
- Use Design Tokens Management plugin (available for desktop and browser versions) to export tokens

2. Replace old tokens with new ones in tailwind\tokens.
3. yarn sync-styles
